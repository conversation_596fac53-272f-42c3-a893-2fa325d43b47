package com.lct.common.dto;

import lombok.Data;

import java.io.Serializable;

/**
 * 分页请求类
 * 
 * <AUTHOR> Team
 * @version 1.0
 */
@Data
public class PageRequest implements Serializable {
    
    private static final long serialVersionUID = 1L;
    
    /**
     * 当前页码，从1开始
     */
    private Integer current = 1;
    
    /**
     * 每页大小
     */
    private Integer size = 10;
    
    /**
     * 排序字段
     */
    private String orderBy;
    
    /**
     * 排序方向：asc/desc
     */
    private String orderDirection = "desc";
    
    /**
     * 获取偏移量
     */
    public Integer getOffset() {
        return (current - 1) * size;
    }
    
    /**
     * 验证分页参数
     */
    public void validate() {
        if (current == null || current < 1) {
            current = 1;
        }
        if (size == null || size < 1) {
            size = 10;
        }
        if (size > 100) {
            size = 100; // 限制最大页面大小
        }
    }
} 