package com.lct.common.config;

import org.springframework.boot.autoconfigure.condition.ConditionalOnMissingBean;
import org.springframework.cloud.client.discovery.EnableDiscoveryClient;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Import;
import org.springframework.web.client.RestTemplate;

/**
 * 微服务自动配置类
 * 提供微服务通用配置，各个服务引入lct-common后自动获得这些配置
 * 
 * <AUTHOR> Team
 * @version 1.0
 */
@Configuration
@EnableDiscoveryClient
@Import({
    RedisConfig.class
})
public class MicroserviceAutoConfiguration {
    
    /**
     * RestTemplate Bean
     */
    @Bean
    @ConditionalOnMissingBean
    public RestTemplate restTemplate() {
        return new RestTemplate();
    }
} 