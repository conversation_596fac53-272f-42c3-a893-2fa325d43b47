package com.lct.common.constant;

/**
 * 错误码常量
 * 
 * <AUTHOR> Team
 * @version 1.0
 */
public class ErrorCode {
    
    // 通用错误码
    public static final int SUCCESS = 200;
    public static final int INTERNAL_ERROR = 500;
    public static final int BAD_REQUEST = 400;
    public static final int UNAUTHORIZED = 401;
    public static final int FORBIDDEN = 403;
    public static final int NOT_FOUND = 404;
    
    // 认证相关错误码
    public static final int AUTH_FAILED = 1001;
    public static final int TOKEN_EXPIRED = 1002;
    public static final int TOKEN_INVALID = 1003;
    public static final int PERMISSION_DENIED = 1004;
    public static final int SIGNATURE_INVALID = 1005;
    
    // 用户相关错误码
    public static final int USER_NOT_FOUND = 2001;
    public static final int USER_ALREADY_EXISTS = 2002;
    public static final int USER_DISABLED = 2003;
    public static final int USER_BANNED = 2004;
    
    // 钱包相关错误码
    public static final int WALLET_NOT_FOUND = 3001;
    public static final int INSUFFICIENT_BALANCE = 3002;
    public static final int WALLET_FROZEN = 3003;
    public static final int INVALID_ADDRESS = 3004;
    
    // 交易相关错误码
    public static final int EXCHANGE_FAILED = 4001;
    public static final int INVALID_AMOUNT = 4002;
    public static final int RATE_NOT_FOUND = 4003;
    public static final int TRANSACTION_FAILED = 4004;
    
    // 代理相关错误码
    public static final int AGENT_NOT_FOUND = 5001;
    public static final int INVALID_AGENT_LEVEL = 5002;
    public static final int COMMISSION_FAILED = 5003;
    
    // 区块链相关错误码
    public static final int BLOCKCHAIN_ERROR = 6001;
    public static final int CONTRACT_CALL_FAILED = 6002;
    public static final int NETWORK_ERROR = 6003;
    public static final int GAS_INSUFFICIENT = 6004;
} 