package com.lct.common.constant;

import java.util.Arrays;
import java.util.List;

/**
 * 认证相关常量
 * 
 * <AUTHOR> Team
 * @version 1.0
 */
public class AuthConstants {

    /**
     * JWT Token 前缀
     */
    public static final String TOKEN_PREFIX = "Bearer ";

    /**
     * JWT Token Header名称
     */
    public static final String TOKEN_HEADER = "Authorization";

    /**
     * 用户信息缓存前缀
     */
    public static final String USER_CACHE_PREFIX = "user:info:";

    /**
     * Token缓存前缀
     */
    public static final String TOKEN_CACHE_PREFIX = "token:";

    /**
     * 缓存过期时间（秒）
     */
    public static final long CACHE_EXPIRE_TIME = 7200; // 2小时

    /**
     * 白名单路径 - 不需要认证的接口
     */
    public static final List<String> WHITE_LIST_PATHS = Arrays.asList(
        // 认证相关
        "/api/v1/auth/login",
        "/api/v1/auth/wallet-login",
        "/api/v1/auth/refresh",
        
        // 用户注册
        "/api/v1/users/register",
        "/api/v1/users/check-invitation",
        
        // 健康检查
        "/actuator/**",
        
        // 静态资源
        "/favicon.ico",
        "/error",
        
        // Swagger文档
        "/swagger-ui/**",
        "/v3/api-docs/**",
        
        // 公开的区块链查询接口
        "/api/v1/blockchain/network-info",
        "/api/v1/blockchain/token-info"
    );

    /**
     * 管理员专用路径
     */
    public static final List<String> ADMIN_PATHS = Arrays.asList(
        "/api/v1/admin/**"
    );

    /**
     * 代理专用路径
     */
    public static final List<String> AGENT_PATHS = Arrays.asList(
        "/api/v1/agents/dashboard",
        "/api/v1/agents/team",
        "/api/v1/agents/commission"
    );
} 