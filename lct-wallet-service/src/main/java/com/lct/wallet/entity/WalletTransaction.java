package com.lct.wallet.entity;

import java.math.BigDecimal;

import com.baomidou.mybatisplus.annotation.TableName;
import com.lct.common.entity.BaseEntity;

import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 钱包交易记录实体类
 * 
 * <AUTHOR> Team
 * @version 1.0
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("wa_wallet_transactions")
public class WalletTransaction extends BaseEntity {
    
    /**
     * 钱包ID
     */
    private Long walletId;
    
    /**
     * 用户ID
     */
    private Long userId;
    
    /**
     * 交易哈希
     */
    private String txHash;
    
    /**
     * 区块链网络
     */
    private String chain;
    
    /**
     * 交易类型：DEPOSIT-充值，WITHDRAW-提现，TRANSFER-转账
     */
    private String type;
    
    /**
     * 发送方地址
     */
    private String fromAddress;
    
    /**
     * 接收方地址
     */
    private String toAddress;
    
    /**
     * 代币符号
     */
    private String tokenSymbol;
    
    /**
     * 代币合约地址
     */
    private String contractAddress;
    
    /**
     * 交易金额
     */
    private BigDecimal amount;
    
    /**
     * 手续费
     */
    private BigDecimal fee;
    
    /**
     * 区块高度
     */
    private Long blockNumber;
    
    /**
     * 确认数
     */
    private Integer confirmations;
    
    /**
     * 交易状态：PENDING-待确认，SUCCESS-成功，FAILED-失败
     */
    private String status;
    
    /**
     * 备注
     */
    private String remark;
} 