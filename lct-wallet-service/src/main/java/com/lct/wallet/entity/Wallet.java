package com.lct.wallet.entity;

import java.math.BigDecimal;

import com.baomidou.mybatisplus.annotation.TableName;
import com.lct.common.entity.BaseEntity;

import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 钱包实体类
 * 
 * <AUTHOR> Team
 * @version 1.0
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("wa_wallets")
public class Wallet extends BaseEntity {
    
    /**
     * 用户ID
     */
    private Long userId;
    
    /**
     * 钱包地址
     */
    private String address;
    
    /**
     * 区块链网络：BSC、TRON
     */
    private String chain;
    
    /**
     * 代币符号
     */
    private String tokenSymbol;
    
    /**
     * 代币名称
     */
    private String tokenName;
    
    /**
     * 代币合约地址
     */
    private String contractAddress;
    
    /**
     * 代币精度
     */
    private Integer decimals;
    
    /**
     * 余额
     */
    private BigDecimal balance;
    
    /**
     * 冻结余额
     */
    private BigDecimal frozenBalance;
    
    /**
     * 状态：1-正常，0-禁用
     */
    private Integer status;
    
    /**
     * 是否为主钱包
     */
    private Boolean isPrimary;
} 