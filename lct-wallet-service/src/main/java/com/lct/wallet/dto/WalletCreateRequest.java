package com.lct.wallet.dto;

import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.Pattern;

/**
 * 钱包创建请求DTO
 * 
 * <AUTHOR> Team
 * @version 1.0
 */
@Data
public class WalletCreateRequest {
    
    /**
     * 钱包地址
     */
    @NotBlank(message = "钱包地址不能为空")
    private String address;
    
    /**
     * 区块链网络
     */
    @NotBlank(message = "区块链网络不能为空")
    @Pattern(regexp = "^(BSC|TRON)$", message = "区块链网络只能是BSC或TRON")
    private String chain;
    
    /**
     * 代币符号
     */
    @NotBlank(message = "代币符号不能为空")
    private String tokenSymbol;
    
    /**
     * 代币名称
     */
    private String tokenName;
    
    /**
     * 代币合约地址
     */
    private String contractAddress;
    
    /**
     * 代币精度
     */
    private Integer decimals = 18;
    
    /**
     * 是否为主钱包
     */
    private Boolean isPrimary = false;
} 