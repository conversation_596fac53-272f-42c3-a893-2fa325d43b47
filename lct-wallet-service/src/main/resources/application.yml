server:
  port: 8083

spring:
  application:
    name: lct-wallet-service
  profiles:
    active: dev
  cloud:
    nacos:
      discovery:
        server-addr: ************:8848
        namespace: lct-finance
      config:
        server-addr: ************:8848
        namespace: lct-finance
        file-extension: yml
  
  # 数据库配置
  datasource:
    url: **************************************************************************************************************************
    username: lct_user
    password: lct123456
    driver-class-name: com.mysql.cj.jdbc.Driver
  
  # Redis配置
  redis:
    host: ************
    port: 6379
    database: 3
    timeout: 3000ms
    lettuce:
      pool:
        max-active: 8
        max-idle: 8
        min-idle: 0

# MyBatis Plus配置
mybatis-plus:
  configuration:
    map-underscore-to-camel-case: true
    log-impl: org.apache.ibatis.logging.stdout.StdOutImpl
  global-config:
    db-config:
      logic-delete-field: deleted
      logic-delete-value: 1
      logic-not-delete-value: 0

# 区块链配置
blockchain:
  bsc:
    rpc-url: https://bsc-dataseed1.binance.org/
    chain-id: 56
    gas-price: 5000000000
    gas-limit: 21000
  tron:
    rpc-url: https://api.trongrid.io
    chain-id: 1
    fee-limit: 100000000

# 钱包配置
wallet:
  min-confirmations: 12
  auto-update-balance: true
  update-interval: 60000

# 日志配置
logging:
  level:
    com.lct.wallet: DEBUG
    com.baomidou.mybatisplus: DEBUG 