server:
  port: 8085

spring:
  application:
    name: lct-agent-service
  profiles:
    active: dev
  cloud:
    nacos:
      discovery:
        server-addr: ************:8848
        namespace: lct-finance
      config:
        server-addr: ************:8848
        namespace: lct-finance
        file-extension: yml
  
  # 数据库配置
  datasource:
    url: jdbc:mysql://************:3306/lct_agent?useUnicode=true&characterEncoding=utf8&useSSL=false&serverTimezone=Asia/Shanghai
    username: lct_user
    password: lct123456
    driver-class-name: com.mysql.cj.jdbc.Driver
  
  # Redis配置
  redis:
    host: ************
    port: 6379
    database: 5
    timeout: 3000ms
    lettuce:
      pool:
        max-active: 8
        max-idle: 8
        min-idle: 0

# MyBatis Plus配置
mybatis-plus:
  configuration:
    map-underscore-to-camel-case: true
    log-impl: org.apache.ibatis.logging.stdout.StdOutImpl
  global-config:
    db-config:
      logic-delete-field: deleted
      logic-delete-value: 1
      logic-not-delete-value: 0

# 代理配置
agent:
  commission:
    level1-rate: 0.05    # 一级代理佣金比例
    level2-rate: 0.03    # 二级代理佣金比例
    level3-rate: 0.02    # 三级代理佣金比例
  withdraw:
    min-amount: 100      # 最小提现金额
    max-amount: 50000    # 最大提现金额
    daily-limit: 100000  # 每日提现限额

# 日志配置
logging:
  level:
    com.lct.agent: DEBUG
    com.baomidou.mybatisplus: DEBUG 