package com.lct.agent;

import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;

/**
 * LCT Agent Service 代理服务启动类
 * 
 * 负责代理管理、佣金计算、团队统计等功能
 * 
 * <AUTHOR> Team
 * @version 1.0
 */
@SpringBootApplication(scanBasePackages = {"com.lct.agent", "com.lct.common"})
public class AgentServiceApplication {

    public static void main(String[] args) {
        SpringApplication.run(AgentServiceApplication.class, args);
        System.out.println("=================================");
        System.out.println("LCT代理服务启动成功！");
        System.out.println("服务端口: 8085");
        System.out.println("=================================");
    }
} 