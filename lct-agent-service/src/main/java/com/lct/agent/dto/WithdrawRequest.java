package com.lct.agent.dto;

import java.math.BigDecimal;

/**
 * 提现请求DTO
 * 
 * <AUTHOR> Team
 * @version 1.0
 */
public class WithdrawRequest {
    
    /**
     * 提现金额
     */
    private BigDecimal amount;
    
    /**
     * 提现地址
     */
    private String address;
    
    /**
     * 提现密码
     */
    private String password;

    // Getters and Setters
    public BigDecimal getAmount() {
        return amount;
    }

    public void setAmount(BigDecimal amount) {
        this.amount = amount;
    }

    public String getAddress() {
        return address;
    }

    public void setAddress(String address) {
        this.address = address;
    }

    public String getPassword() {
        return password;
    }

    public void setPassword(String password) {
        this.password = password;
    }
} 