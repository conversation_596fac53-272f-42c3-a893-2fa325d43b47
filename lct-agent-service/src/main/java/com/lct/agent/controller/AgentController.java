package com.lct.agent.controller;

import com.lct.common.dto.PageRequest;
import com.lct.common.dto.Result;
import com.lct.agent.entity.Agent;
import com.lct.agent.dto.AgentApplyRequest;
import com.lct.agent.dto.WithdrawRequest;
import com.lct.agent.service.AgentService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

/**
 * 代理控制器
 * 
 * <AUTHOR> Team
 * @version 1.0
 */
@RestController
@RequestMapping("/agent")
public class AgentController {
    
    @Autowired
    private AgentService agentService;
    
    /**
     * 申请成为代理
     */
    @PostMapping("/apply")
    public Result<Agent> applyAgent(@RequestHeader("Authorization") String token,
                                   @RequestBody AgentApplyRequest request) {
        Agent agent = agentService.applyAgent(token, request.getAgentName(), 
                                            request.getPhone(), request.getEmail(), request.getRegion());
        return Result.success(agent);
    }
    
    /**
     * 获取代理信息
     */
    @GetMapping("/info")
    public Result<Agent> getAgentInfo(@RequestHeader("Authorization") String token) {
        Agent agent = agentService.getAgentInfo(token);
        return Result.success(agent);
    }
    
    /**
     * 获取代理统计数据
     */
    @GetMapping("/statistics")
    public Result<Object> getStatistics(@RequestHeader("Authorization") String token) {
        Object statistics = agentService.getStatistics(token);
        return Result.success(statistics);
    }
    
    /**
     * 获取直推用户列表
     */
    @PostMapping("/direct-users")
    public Result<Object> getDirectUsers(@RequestHeader("Authorization") String token,
                                        @RequestBody PageRequest pageRequest) {
        Object result = agentService.getDirectUsers(token, pageRequest);
        return Result.success(result);
    }
    
    /**
     * 获取团队用户列表
     */
    @PostMapping("/team-users")
    public Result<Object> getTeamUsers(@RequestHeader("Authorization") String token,
                                      @RequestBody PageRequest pageRequest) {
        Object result = agentService.getTeamUsers(token, pageRequest);
        return Result.success(result);
    }
    
    /**
     * 获取佣金记录
     */
    @PostMapping("/commissions")
    public Result<Object> getCommissions(@RequestHeader("Authorization") String token,
                                        @RequestBody PageRequest pageRequest,
                                        @RequestParam(required = false) String type) {
        Object result = agentService.getCommissions(token, pageRequest, type);
        return Result.success(result);
    }
    
    /**
     * 提现佣金
     */
    @PostMapping("/withdraw")
    public Result<Object> withdrawCommission(@RequestHeader("Authorization") String token,
                                            @RequestBody WithdrawRequest request) {
        Object result = agentService.withdrawCommission(token, request.getAmount(), request.getAddress());
        return Result.success(result);
    }
    
    /**
     * 获取代理等级配置
     */
    @GetMapping("/levels")
    public Result<Object> getAgentLevels() {
        Object result = agentService.getAgentLevels();
        return Result.success(result);
    }
    
    /**
     * 审核代理申请（管理员接口）
     */
    @PostMapping("/audit/{agentId}")
    public Result<String> auditAgent(@PathVariable Long agentId,
                                    @RequestParam Integer auditStatus,
                                    @RequestParam(required = false) String auditRemark) {
        agentService.auditAgent(agentId, auditStatus, auditRemark);
        return Result.success("审核完成");
    }
} 