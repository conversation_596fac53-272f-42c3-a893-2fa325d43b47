package com.lct.agent.service.impl;

import com.lct.agent.entity.Agent;
import com.lct.agent.entity.Commission;
import com.lct.agent.mapper.AgentMapper;
import com.lct.agent.mapper.CommissionMapper;
import com.lct.agent.service.AgentService;
import com.lct.common.dto.PageRequest;
import com.lct.common.dto.Result;
import com.lct.common.exception.BusinessException;
import com.lct.common.utils.JwtUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.UUID;

/**
 * 代理服务实现类
 * 
 * <AUTHOR> Team
 * @version 1.0
 */
@Service
@Transactional
public class AgentServiceImpl implements AgentService {
    
    @Autowired
    private AgentMapper agentMapper;
    
    @Autowired
    private CommissionMapper commissionMapper;
    
    @Override
    public Agent applyAgent(String token, String agentName, String phone, String email, String region) {
        // 解析token获取用户ID
        Long userId = JwtUtils.getUserIdFromToken(token.replace("Bearer ", ""));
        
        // 检查是否已经是代理
        Agent existingAgent = agentMapper.findByUserId(userId);
        if (existingAgent != null) {
            throw new BusinessException("您已经是代理，无需重复申请");
        }
        
        // 创建代理申请
        Agent agent = new Agent();
        agent.setUserId(userId);
        agent.setAgentName(agentName);
        agent.setPhone(phone);
        agent.setEmail(email);
        agent.setRegion(region);
        agent.setAgentCode(generateAgentCode());
        agent.setLevel(1); // 默认一级代理
        agent.setCommissionRate(new BigDecimal("0.05")); // 5%佣金比例
        agent.setTotalCommission(BigDecimal.ZERO);
        agent.setAvailableCommission(BigDecimal.ZERO);
        agent.setWithdrawnCommission(BigDecimal.ZERO);
        agent.setDirectUserCount(0);
        agent.setTeamUserCount(0);
        agent.setTeamPerformance(BigDecimal.ZERO);
        agent.setStatus(1); // 正常状态
        agent.setAuditStatus(0); // 待审核
        
        agentMapper.insert(agent);
        return agent;
    }
    
    @Override
    public Agent getAgentInfo(String token) {
        Long userId = JwtUtils.getUserIdFromToken(token.replace("Bearer ", ""));
        Agent agent = agentMapper.findByUserId(userId);
        if (agent == null) {
            throw new BusinessException("您还不是代理");
        }
        return agent;
    }
    
    @Override
    public Object getStatistics(String token) {
        Long userId = JwtUtils.getUserIdFromToken(token.replace("Bearer ", ""));
        Agent agent = agentMapper.findByUserId(userId);
        if (agent == null) {
            throw new BusinessException("您还不是代理");
        }
        
        Map<String, Object> statistics = new HashMap<>();
        statistics.put("totalCommission", agent.getTotalCommission());
        statistics.put("availableCommission", agent.getAvailableCommission());
        statistics.put("withdrawnCommission", agent.getWithdrawnCommission());
        statistics.put("directUserCount", agent.getDirectUserCount());
        statistics.put("teamUserCount", agent.getTeamUserCount());
        statistics.put("teamPerformance", agent.getTeamPerformance());
        
        return statistics;
    }
    
    @Override
    public Object getDirectUsers(String token, PageRequest pageRequest) {
        Long userId = JwtUtils.getUserIdFromToken(token.replace("Bearer ", ""));
        Agent agent = agentMapper.findByUserId(userId);
        if (agent == null) {
            throw new BusinessException("您还不是代理");
        }
        
        List<Agent> directAgents = agentMapper.findDirectAgents(agent.getId());
        return directAgents;
    }
    
    @Override
    public Object getTeamUsers(String token, PageRequest pageRequest) {
        // 实现团队用户查询逻辑
        return getDirectUsers(token, pageRequest);
    }
    
    @Override
    public Object getCommissions(String token, PageRequest pageRequest, String type) {
        Long userId = JwtUtils.getUserIdFromToken(token.replace("Bearer ", ""));
        Agent agent = agentMapper.findByUserId(userId);
        if (agent == null) {
            throw new BusinessException("您还不是代理");
        }
        
        List<Commission> commissions;
        if (type != null && !type.isEmpty()) {
            commissions = commissionMapper.findByAgentIdAndType(agent.getId(), Integer.parseInt(type));
        } else {
            commissions = commissionMapper.findByAgentId(agent.getId());
        }
        
        return commissions;
    }
    
    @Override
    public Object withdrawCommission(String token, BigDecimal amount, String address) {
        Long userId = JwtUtils.getUserIdFromToken(token.replace("Bearer ", ""));
        Agent agent = agentMapper.findByUserId(userId);
        if (agent == null) {
            throw new BusinessException("您还不是代理");
        }
        
        // 检查提现金额
        if (amount.compareTo(new BigDecimal("100")) < 0) {
            throw new BusinessException("最小提现金额为100");
        }
        
        if (amount.compareTo(agent.getAvailableCommission()) > 0) {
            throw new BusinessException("提现金额超过可用余额");
        }
        
        // 创建提现记录（这里简化处理）
        Map<String, Object> result = new HashMap<>();
        result.put("withdrawId", UUID.randomUUID().toString());
        result.put("amount", amount);
        result.put("address", address);
        result.put("status", "pending");
        result.put("createTime", LocalDateTime.now());
        
        return result;
    }
    
    @Override
    public Object getAgentLevels() {
        Map<String, Object> levels = new HashMap<>();
        levels.put("level1", Map.of("name", "一级代理", "rate", "5%", "requirement", "直推5人"));
        levels.put("level2", Map.of("name", "二级代理", "rate", "3%", "requirement", "团队20人"));
        levels.put("level3", Map.of("name", "三级代理", "rate", "2%", "requirement", "团队50人"));
        return levels;
    }
    
    @Override
    public void calculateCommission(Long userId, BigDecimal amount, String orderType) {
        // 实现佣金计算逻辑
        // 这里是核心的佣金分配算法
    }
    
    @Override
    public void auditAgent(Long agentId, Integer auditStatus, String auditRemark) {
        Agent agent = agentMapper.selectById(agentId);
        if (agent == null) {
            throw new BusinessException("代理不存在");
        }
        
        agent.setAuditStatus(auditStatus);
        agent.setAuditRemark(auditRemark);
        agentMapper.updateById(agent);
    }
    
    /**
     * 生成代理编号
     */
    private String generateAgentCode() {
        return "AG" + System.currentTimeMillis();
    }
} 