package com.lct.agent.service;

import com.lct.agent.entity.Agent;
import com.lct.agent.entity.Commission;
import com.lct.common.dto.PageRequest;

import java.math.BigDecimal;

/**
 * 代理服务接口
 * 
 * <AUTHOR> Team
 * @version 1.0
 */
public interface AgentService {
    
    /**
     * 申请成为代理
     */
    Agent applyAgent(String token, String agentName, String phone, String email, String region);
    
    /**
     * 获取代理信息
     */
    Agent getAgentInfo(String token);
    
    /**
     * 获取代理统计数据
     */
    Object getStatistics(String token);
    
    /**
     * 获取直推用户列表
     */
    Object getDirectUsers(String token, PageRequest pageRequest);
    
    /**
     * 获取团队用户列表
     */
    Object getTeamUsers(String token, PageRequest pageRequest);
    
    /**
     * 获取佣金记录
     */
    Object getCommissions(String token, PageRequest pageRequest, String type);
    
    /**
     * 提现佣金
     */
    Object withdrawCommission(String token, BigDecimal amount, String address);
    
    /**
     * 获取代理等级配置
     */
    Object getAgentLevels();
    
    /**
     * 计算佣金
     */
    void calculateCommission(Long userId, BigDecimal amount, String orderType);
    
    /**
     * 审核代理申请
     */
    void auditAgent(Long agentId, Integer auditStatus, String auditRemark);
} 