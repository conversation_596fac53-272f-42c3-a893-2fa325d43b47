package com.lct.agent.entity;

import java.math.BigDecimal;

import com.baomidou.mybatisplus.annotation.TableName;
import com.lct.common.entity.BaseEntity;

import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 佣金记录实体类
 * 
 * <AUTHOR> Team
 * @version 1.0
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("wa_agent_commissions")
public class Commission extends BaseEntity {
    
    /**
     * 代理ID
     */
    private Long agentId;
    
    /**
     * 用户ID（产生佣金的用户）
     */
    private Long userId;
    
    /**
     * 佣金类型：1-直推佣金，2-团队佣金，3-级差佣金
     */
    private Integer type;
    
    /**
     * 佣金来源类型：1-兑换佣金，2-销毁佣金，3-邀请奖励
     */
    private Integer sourceType;
    
    /**
     * 来源订单ID
     */
    private Long sourceOrderId;
    
    /**
     * 佣金金额
     */
    private BigDecimal amount;
    
    /**
     * 佣金比例
     */
    private BigDecimal rate;
    
    /**
     * 原始金额（计算佣金的基础金额）
     */
    private BigDecimal originalAmount;
    
    /**
     * 代理等级
     */
    private Integer agentLevel;
    
    /**
     * 状态：0-待发放，1-已发放，2-已取消
     */
    private Integer status;
    
    /**
     * 发放时间
     */
    private java.time.LocalDateTime grantTime;
    
    /**
     * 备注
     */
    private String remark;
} 