package com.lct.agent.entity;

import java.math.BigDecimal;

import com.baomidou.mybatisplus.annotation.TableName;
import com.lct.common.entity.BaseEntity;

import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 代理实体类
 * 
 * <AUTHOR> Team
 * @version 1.0
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("wa_agents")
public class Agent extends BaseEntity {
    
    /**
     * 用户ID
     */
    private Long userId;
    
    /**
     * 代理等级：1-一级代理，2-二级代理，3-三级代理
     */
    private Integer level;
    
    /**
     * 上级代理ID
     */
    private Long parentAgentId;
    
    /**
     * 代理编号
     */
    private String agentCode;
    
    /**
     * 代理名称
     */
    private String agentName;
    
    /**
     * 联系电话
     */
    private String phone;
    
    /**
     * 邮箱
     */
    private String email;
    
    /**
     * 地区
     */
    private String region;
    
    /**
     * 佣金比例
     */
    private BigDecimal commissionRate;
    
    /**
     * 累计佣金
     */
    private BigDecimal totalCommission;
    
    /**
     * 可提现佣金
     */
    private BigDecimal availableCommission;
    
    /**
     * 已提现佣金
     */
    private BigDecimal withdrawnCommission;
    
    /**
     * 直推用户数
     */
    private Integer directUserCount;
    
    /**
     * 团队用户数
     */
    private Integer teamUserCount;
    
    /**
     * 团队业绩
     */
    private BigDecimal teamPerformance;
    
    /**
     * 状态：1-正常，0-禁用
     */
    private Integer status;
    
    /**
     * 审核状态：0-待审核，1-已通过，2-已拒绝
     */
    private Integer auditStatus;
    
    /**
     * 审核备注
     */
    private String auditRemark;
} 