package com.lct.agent.config;

import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

import java.math.BigDecimal;

/**
 * 代理服务配置类
 * 
 * <AUTHOR> Team
 * @version 1.0
 */
@Configuration
@ConfigurationProperties(prefix = "agent")
public class AgentConfig {
    
    /**
     * 佣金配置
     */
    private Commission commission = new Commission();
    
    /**
     * 提现配置
     */
    private Withdraw withdraw = new Withdraw();
    
    public static class Commission {
        /**
         * 一级代理佣金比例
         */
        private BigDecimal level1Rate = new BigDecimal("0.05");
        
        /**
         * 二级代理佣金比例
         */
        private BigDecimal level2Rate = new BigDecimal("0.03");
        
        /**
         * 三级代理佣金比例
         */
        private BigDecimal level3Rate = new BigDecimal("0.02");

        // Getters and Setters
        public BigDecimal getLevel1Rate() {
            return level1Rate;
        }

        public void setLevel1Rate(BigDecimal level1Rate) {
            this.level1Rate = level1Rate;
        }

        public BigDecimal getLevel2Rate() {
            return level2Rate;
        }

        public void setLevel2Rate(BigDecimal level2Rate) {
            this.level2Rate = level2Rate;
        }

        public BigDecimal getLevel3Rate() {
            return level3Rate;
        }

        public void setLevel3Rate(BigDecimal level3Rate) {
            this.level3Rate = level3Rate;
        }
    }
    
    public static class Withdraw {
        /**
         * 最小提现金额
         */
        private BigDecimal minAmount = new BigDecimal("100");
        
        /**
         * 最大提现金额
         */
        private BigDecimal maxAmount = new BigDecimal("50000");
        
        /**
         * 每日提现限额
         */
        private BigDecimal dailyLimit = new BigDecimal("100000");

        // Getters and Setters
        public BigDecimal getMinAmount() {
            return minAmount;
        }

        public void setMinAmount(BigDecimal minAmount) {
            this.minAmount = minAmount;
        }

        public BigDecimal getMaxAmount() {
            return maxAmount;
        }

        public void setMaxAmount(BigDecimal maxAmount) {
            this.maxAmount = maxAmount;
        }

        public BigDecimal getDailyLimit() {
            return dailyLimit;
        }

        public void setDailyLimit(BigDecimal dailyLimit) {
            this.dailyLimit = dailyLimit;
        }
    }

    // Getters and Setters
    public Commission getCommission() {
        return commission;
    }

    public void setCommission(Commission commission) {
        this.commission = commission;
    }

    public Withdraw getWithdraw() {
        return withdraw;
    }

    public void setWithdraw(Withdraw withdraw) {
        this.withdraw = withdraw;
    }
} 