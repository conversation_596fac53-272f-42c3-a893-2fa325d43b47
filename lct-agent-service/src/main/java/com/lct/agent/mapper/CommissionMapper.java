package com.lct.agent.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.lct.agent.entity.Commission;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.math.BigDecimal;
import java.util.List;

/**
 * 佣金数据访问层
 * 
 * <AUTHOR> Team
 * @version 1.0
 */
@Mapper
public interface CommissionMapper extends BaseMapper<Commission> {
    
    /**
     * 查询代理的佣金记录
     */
    @Select("SELECT * FROM wa_agent_commissions WHERE agent_id = #{agentId} AND deleted = 0 ORDER BY created_at DESC")
    List<Commission> findByAgentId(@Param("agentId") Long agentId);
    
    /**
     * 查询代理的总佣金
     */
    @Select("SELECT COALESCE(SUM(amount), 0) FROM wa_agent_commissions WHERE agent_id = #{agentId} AND status = 1 AND deleted = 0")
    BigDecimal getTotalCommission(@Param("agentId") Long agentId);
    
    /**
     * 查询代理的可提现佣金
     */
    @Select("SELECT COALESCE(SUM(amount), 0) FROM wa_agent_commissions WHERE agent_id = #{agentId} AND status = 1 AND deleted = 0")
    BigDecimal getAvailableCommission(@Param("agentId") Long agentId);
    
    /**
     * 根据类型查询佣金记录
     */
    @Select("SELECT * FROM wa_agent_commissions WHERE agent_id = #{agentId} AND type = #{type} AND deleted = 0 ORDER BY created_at DESC")
    List<Commission> findByAgentIdAndType(@Param("agentId") Long agentId, @Param("type") Integer type);
} 