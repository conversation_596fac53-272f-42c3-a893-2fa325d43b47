package com.lct.agent.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.lct.agent.entity.Agent;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

/**
 * 代理数据访问层
 * 
 * <AUTHOR> Team
 * @version 1.0
 */
@Mapper
public interface AgentMapper extends BaseMapper<Agent> {
    
    /**
     * 根据用户ID查询代理信息
     */
    @Select("SELECT * FROM wa_agents WHERE user_id = #{userId} AND deleted = 0")
    Agent findByUserId(@Param("userId") Long userId);
    
    /**
     * 查询代理的直推用户列表
     */
    @Select("SELECT a.* FROM wa_agents a WHERE a.parent_agent_id = #{agentId} AND a.deleted = 0")
    List<Agent> findDirectAgents(@Param("agentId") Long agentId);
    
    /**
     * 查询代理的团队用户数量
     */
    @Select("SELECT COUNT(*) FROM wa_agents WHERE parent_agent_id = #{agentId} AND deleted = 0")
    Integer countTeamUsers(@Param("agentId") Long agentId);
    
    /**
     * 根据代理编号查询代理
     */
    @Select("SELECT * FROM wa_agents WHERE agent_code = #{agentCode} AND deleted = 0")
    Agent findByAgentCode(@Param("agentCode") String agentCode);
} 