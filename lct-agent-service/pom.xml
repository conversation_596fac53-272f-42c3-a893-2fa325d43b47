<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 
         https://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    
    <parent>
        <groupId>com.lct</groupId>
        <artifactId>lct-finance-microservices</artifactId>
        <version>1.0.0</version>
    </parent>
    
    <artifactId>lct-agent-service</artifactId>
    <name>LCT Agent Service</name>
    <description>LCT金融系统代理服务 - 负责代理管理、佣金计算、团队统计等功能</description>
    
    <dependencies>
        <!-- LCT Common - 包含所有微服务基础依赖 -->
        <dependency>
            <groupId>com.lct</groupId>
            <artifactId>lct-common</artifactId>
        </dependency>
        
        <!-- RabbitMQ 消息队列 - 用于异步处理佣金计算 -->
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-amqp</artifactId>
        </dependency>
        
        <!-- Spring Boot Mail - 用于邮件通知 -->
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-mail</artifactId>
        </dependency>
    </dependencies>
    
    <build>
        <plugins>
            <plugin>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-maven-plugin</artifactId>
                <configuration>
                    <excludes>
                        <exclude>
                            <groupId>org.projectlombok</groupId>
                            <artifactId>lombok</artifactId>
                        </exclude>
                    </excludes>
                </configuration>
            </plugin>
        </plugins>
    </build>
</project> 