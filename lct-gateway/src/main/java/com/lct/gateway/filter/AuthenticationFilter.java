package com.lct.gateway.filter;

import com.lct.common.constant.AuthConstants;
import com.lct.common.utils.JwtUtils;
import com.lct.common.utils.RedisUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cloud.gateway.filter.GatewayFilterChain;
import org.springframework.cloud.gateway.filter.GlobalFilter;
import org.springframework.core.Ordered;
import org.springframework.http.HttpStatus;
import org.springframework.http.server.reactive.ServerHttpRequest;
import org.springframework.http.server.reactive.ServerHttpResponse;
import org.springframework.stereotype.Component;
import org.springframework.util.AntPathMatcher;
import org.springframework.util.StringUtils;
import org.springframework.web.server.ServerWebExchange;
import reactor.core.publisher.Mono;

import java.util.concurrent.TimeUnit;

/**
 * 统一认证过滤器
 * 
 * <AUTHOR> Team
 * @version 1.0
 */
@Slf4j
@Component
public class AuthenticationFilter implements GlobalFilter, Ordered {

    @Autowired
    private JwtUtils jwtUtils;

    @Autowired
    private RedisUtils redisUtils;

    private final AntPathMatcher pathMatcher = new AntPathMatcher();

    @Override
    public Mono<Void> filter(ServerWebExchange exchange, GatewayFilterChain chain) {
        ServerHttpRequest request = exchange.getRequest();
        String path = request.getURI().getPath();
        
        log.debug("请求路径: {}", path);

        // 检查是否为白名单路径
        if (isWhiteListPath(path)) {
            log.debug("白名单路径，跳过认证: {}", path);
            return chain.filter(exchange);
        }

        // 获取Token
        String token = getTokenFromRequest(request);
        if (!StringUtils.hasText(token)) {
            log.warn("请求缺少Token: {}", path);
            return unauthorizedResponse(exchange.getResponse());
        }

        try {
            // 验证Token
            if (!jwtUtils.validateToken(token)) {
                log.warn("Token验证失败: {}", token);
                return unauthorizedResponse(exchange.getResponse());
            }

            // 从Token中获取用户信息
            String userId = jwtUtils.getUserIdFromToken(token);
            String userType = jwtUtils.getUserTypeFromToken(token);
            
            if (!StringUtils.hasText(userId)) {
                log.warn("Token中缺少用户信息: {}", token);
                return unauthorizedResponse(exchange.getResponse());
            }

            // 检查用户权限
            if (!checkUserPermission(path, userType)) {
                log.warn("用户权限不足: userId={}, userType={}, path={}", userId, userType, path);
                return forbiddenResponse(exchange.getResponse());
            }

            // 检查Redis中的用户缓存
            String userCacheKey = AuthConstants.USER_CACHE_PREFIX + userId;
            Object userInfo = redisUtils.get(userCacheKey);
            
            if (userInfo == null) {
                // 用户信息不在缓存中，可能需要重新登录
                log.warn("用户缓存已过期: userId={}", userId);
                return unauthorizedResponse(exchange.getResponse());
            }

            // 刷新用户缓存过期时间
            redisUtils.expire(userCacheKey, AuthConstants.CACHE_EXPIRE_TIME, TimeUnit.SECONDS);

            // 在请求头中添加用户信息，供下游服务使用
            ServerHttpRequest modifiedRequest = request.mutate()
                    .header("X-User-Id", userId)
                    .header("X-User-Type", userType)
                    .build();

            log.debug("认证成功: userId={}, userType={}, path={}", userId, userType, path);
            
            return chain.filter(exchange.mutate().request(modifiedRequest).build());

        } catch (Exception e) {
            log.error("Token验证异常: {}", e.getMessage(), e);
            return unauthorizedResponse(exchange.getResponse());
        }
    }

    /**
     * 检查是否为白名单路径
     */
    private boolean isWhiteListPath(String path) {
        return AuthConstants.WHITE_LIST_PATHS.stream()
                .anyMatch(pattern -> pathMatcher.match(pattern, path));
    }

    /**
     * 检查用户权限
     */
    private boolean checkUserPermission(String path, String userType) {
        // 检查管理员专用路径
        boolean isAdminPath = AuthConstants.ADMIN_PATHS.stream()
                .anyMatch(pattern -> pathMatcher.match(pattern, path));
        if (isAdminPath && !"ADMIN".equals(userType)) {
            return false;
        }

        // 检查代理专用路径
        boolean isAgentPath = AuthConstants.AGENT_PATHS.stream()
                .anyMatch(pattern -> pathMatcher.match(pattern, path));
        if (isAgentPath && !"AGENT".equals(userType) && !"ADMIN".equals(userType)) {
            return false;
        }

        return true;
    }

    /**
     * 从请求中获取Token
     */
    private String getTokenFromRequest(ServerHttpRequest request) {
        String authHeader = request.getHeaders().getFirst(AuthConstants.TOKEN_HEADER);
        if (StringUtils.hasText(authHeader) && authHeader.startsWith(AuthConstants.TOKEN_PREFIX)) {
            return authHeader.substring(AuthConstants.TOKEN_PREFIX.length());
        }
        return null;
    }

    /**
     * 返回401未授权响应
     */
    private Mono<Void> unauthorizedResponse(ServerHttpResponse response) {
        response.setStatusCode(HttpStatus.UNAUTHORIZED);
        response.getHeaders().add("Content-Type", "application/json;charset=UTF-8");
        String body = "{\"code\":401,\"message\":\"未授权访问\",\"data\":null}";
        return response.writeWith(Mono.just(response.bufferFactory().wrap(body.getBytes())));
    }

    /**
     * 返回403禁止访问响应
     */
    private Mono<Void> forbiddenResponse(ServerHttpResponse response) {
        response.setStatusCode(HttpStatus.FORBIDDEN);
        response.getHeaders().add("Content-Type", "application/json;charset=UTF-8");
        String body = "{\"code\":403,\"message\":\"权限不足\",\"data\":null}";
        return response.writeWith(Mono.just(response.bufferFactory().wrap(body.getBytes())));
    }

    @Override
    public int getOrder() {
        return -100; // 优先级较高，在其他过滤器之前执行
    }
} 