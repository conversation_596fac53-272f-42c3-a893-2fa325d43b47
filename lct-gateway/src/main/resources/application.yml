server:
  port: 8080

spring:
  application:
    name: lct-gateway
  profiles:
    active: dev
  cloud:
    nacos:
      discovery:
        server-addr: ************:8848
        namespace: lct-finance
      config:
        server-addr: ************:8848
        namespace: lct-finance
        file-extension: yml
    gateway:
      discovery:
        locator:
          enabled: true
          lower-case-service-id: true
      routes:
        # 认证服务路由
        - id: lct-auth-service
          uri: lb://lct-auth-service
          predicates:
            - Path=/api/v1/auth/**
          filters:
            - StripPrefix=2
        
        # 用户服务路由
        - id: lct-user-service
          uri: lb://lct-user-service
          predicates:
            - Path=/api/v1/users/**
          filters:
            - StripPrefix=2
        
        # 钱包服务路由
        - id: lct-wallet-service
          uri: lb://lct-wallet-service
          predicates:
            - Path=/api/v1/wallets/**
          filters:
            - StripPrefix=2
        
        # 交易服务路由
        - id: lct-exchange-service
          uri: lb://lct-exchange-service
          predicates:
            - Path=/api/v1/exchange/**
          filters:
            - StripPrefix=2
        
        # 代理服务路由
        - id: lct-agent-service
          uri: lb://lct-agent-service
          predicates:
            - Path=/api/v1/agents/**
          filters:
            - StripPrefix=2
        
        # 管理服务路由
        - id: lct-admin-service
          uri: lb://lct-admin-service
          predicates:
            - Path=/api/v1/admin/**
          filters:
            - StripPrefix=2
        
        # 通知服务路由
        - id: lct-notification-service
          uri: lb://lct-notification-service
          predicates:
            - Path=/api/v1/notifications/**
          filters:
            - StripPrefix=2
        
        # 区块链服务路由
        - id: lct-blockchain-service
          uri: lb://lct-blockchain-service
          predicates:
            - Path=/api/v1/blockchain/**
          filters:
            - StripPrefix=2

# Redis配置
  redis:
    host: ************
    port: 6379
    database: 0
    timeout: 3000ms
    lettuce:
      pool:
        max-active: 8
        max-idle: 8
        min-idle: 0

# 管理端点配置
management:
  endpoints:
    web:
      exposure:
        include: health,info,gateway
  endpoint:
    health:
      show-details: always

# 日志配置
logging:
  level:
    com.lct.gateway: DEBUG
    org.springframework.cloud.gateway: DEBUG 