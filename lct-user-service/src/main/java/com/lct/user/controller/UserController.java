package com.lct.user.controller;

import com.lct.common.dto.PageRequest;
import com.lct.common.dto.Result;
import com.lct.user.dto.UserRegisterRequest;
import com.lct.user.dto.UserUpdateRequest;
import com.lct.user.entity.User;
import com.lct.user.service.UserService;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;

/**
 * 用户控制器
 * 
 * <AUTHOR> Team
 * @version 1.0
 */
@RestController
@RequestMapping("/user")
@RequiredArgsConstructor
public class UserController {
    
    private final UserService userService;
    
    /**
     * 用户注册
     */
    @PostMapping("/register")
    public Result<User> register(@Valid @RequestBody UserRegisterRequest request) {
        User user = userService.register(request);
        return Result.success("注册成功", user);
    }
    
    /**
     * 获取用户信息
     */
    @GetMapping("/info")
    public Result<User> getUserInfo(@RequestHeader("Authorization") String token) {
        User user = userService.getUserInfo(token);
        return Result.success("获取成功", user);
    }
    
    /**
     * 更新用户信息
     */
    @PutMapping("/update")
    public Result<User> updateUser(@RequestHeader("Authorization") String token,
                                  @Valid @RequestBody UserUpdateRequest request) {
        User user = userService.updateUser(token, request);
        return Result.success("更新成功", user);
    }
    
    /**
     * 根据地址获取用户信息
     */
    @GetMapping("/address/{address}")
    public Result<User> getUserByAddress(@PathVariable String address) {
        User user = userService.getUserByAddress(address);
        return Result.success("获取成功", user);
    }
    
    /**
     * 获取用户列表（分页）
     */
    @PostMapping("/list")
    public Result<Object> getUserList(@RequestBody PageRequest pageRequest) {
        Object result = userService.getUserList(pageRequest);
        return Result.success("获取成功", result);
    }
    
    /**
     * 获取邀请的用户列表
     */
    @PostMapping("/invites")
    public Result<Object> getInviteList(@RequestHeader("Authorization") String token,
                                       @RequestBody PageRequest pageRequest) {
        Object result = userService.getInviteList(token, pageRequest);
        return Result.success("获取成功", result);
    }
    
    /**
     * 生成邀请码
     */
    @PostMapping("/invite-code/generate")
    public Result<String> generateInviteCode(@RequestHeader("Authorization") String token) {
        String inviteCode = userService.generateInviteCode(token);
        return Result.success("生成成功", inviteCode);
    }
} 