package com.lct.user.dto;

import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.Pattern;

/**
 * 用户注册请求DTO
 * 
 * <AUTHOR> Team
 * @version 1.0
 */
@Data
public class UserRegisterRequest {
    
    /**
     * 钱包地址
     */
    @NotBlank(message = "钱包地址不能为空")
    private String address;
    
    /**
     * 签名
     */
    @NotBlank(message = "签名不能为空")
    private String signature;
    
    /**
     * 签名消息
     */
    @NotBlank(message = "签名消息不能为空")
    private String message;
    
    /**
     * 区块链网络
     */
    @Pattern(regexp = "^(BSC|TRON)$", message = "区块链网络只能是BSC或TRON")
    private String chain = "BSC";
    
    /**
     * 邀请码
     */
    private String inviteCode;
    
    /**
     * 用户昵称
     */
    private String nickname;
    
    /**
     * 邮箱
     */
    private String email;
} 