server:
  port: 8084

spring:
  application:
    name: lct-exchange-service
  profiles:
    active: dev
  cloud:
    nacos:
      discovery:
        server-addr: ************:8848
        namespace: lct-finance
      config:
        server-addr: ************:8848
        namespace: lct-finance
        file-extension: yml
  
  # 数据库配置
  datasource:
    url: ****************************************************************************************************************************
    username: lct_user
    password: lct123456
    driver-class-name: com.mysql.cj.jdbc.Driver
  
  # Redis配置
  redis:
    host: ************
    port: 6379
    database: 4
    timeout: 3000ms
    lettuce:
      pool:
        max-active: 8
        max-idle: 8
        min-idle: 0

  # RabbitMQ配置
  rabbitmq:
    host: ************
    port: 5672
    username: admin
    password: admin123456
    virtual-host: /

# MyBatis Plus配置
mybatis-plus:
  configuration:
    map-underscore-to-camel-case: true
    log-impl: org.apache.ibatis.logging.stdout.StdOutImpl
  global-config:
    db-config:
      logic-delete-field: deleted
      logic-delete-value: 1
      logic-not-delete-value: 0

# 交易配置
exchange:
  min-amount: 10
  max-amount: 100000
  fee-rate: 0.003
  daily-limit: 500000

# 日志配置
logging:
  level:
    com.lct.exchange: DEBUG
    com.baomidou.mybatisplus: DEBUG 