# LCT金融平台首页开发文档

## 文档信息
- **项目名称**: LCT WEB金融平台
- **模块**: 首页功能
- **版本**: v2.0
- **创建日期**: 2024-12-19
- **技术栈**: PHP 8.0+ / Webman / MySQL / Redis / Web3.js

---

## 1. 数据库结构设计

### 1.1 核心数据表

#### 用户表 (wa_users)
```sql
CREATE TABLE `wa_users` (
  `id` int(10) unsigned NOT NULL AUTO_INCREMENT COMMENT '主键',
  `username` varchar(32) NOT NULL COMMENT '用户名',
  `nickname` varchar(40) NOT NULL COMMENT '昵称',
  `password` varchar(255) NOT NULL COMMENT '密码',
  `user_address` varchar(255) DEFAULT NULL COMMENT '钱包地址',
  `sex` enum('0','1') NOT NULL DEFAULT '1' COMMENT '性别',
  `avatar` varchar(255) DEFAULT NULL COMMENT '头像',
  `email` varchar(128) DEFAULT NULL COMMENT '邮箱',
  `mobile` varchar(16) DEFAULT NULL COMMENT '手机',
  `level` tinyint(4) NOT NULL DEFAULT '0' COMMENT '等级',
  `money` decimal(10,2) NOT NULL DEFAULT '0.00' COMMENT '余额(元)',
  `score` int(11) NOT NULL DEFAULT '0' COMMENT '积分',
  `last_time` datetime DEFAULT NULL COMMENT '登录时间',
  `last_ip` varchar(50) DEFAULT NULL COMMENT '登录ip',
  `join_time` datetime DEFAULT NULL COMMENT '注册时间',
  `join_ip` varchar(50) DEFAULT NULL COMMENT '注册ip',
  `token` varchar(50) DEFAULT NULL COMMENT 'token',
  `ban_end_at` datetime DEFAULT NULL COMMENT '封禁结束时间',
  `created_at` datetime DEFAULT NULL COMMENT '创建时间',
  `updated_at` datetime DEFAULT NULL COMMENT '更新时间',
  `role` int(11) NOT NULL DEFAULT '1' COMMENT '角色',
  `status` tinyint(4) NOT NULL DEFAULT '0' COMMENT '状态 0正常 1禁用',
  PRIMARY KEY (`id`),
  UNIQUE KEY `username` (`username`),
  KEY `user_address` (`user_address`),
  KEY `join_time` (`join_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='用户表';
```

#### 钱包表 (wallets)
```sql
CREATE TABLE `wallets` (
  `id` int(10) unsigned NOT NULL AUTO_INCREMENT COMMENT '主键',
  `user_id` int(10) unsigned NOT NULL COMMENT '用户id',
  `available` decimal(20,8) NOT NULL DEFAULT '0.00000000' COMMENT '可用数量(可提现)',
  `frozen` decimal(20,8) NOT NULL DEFAULT '0.00000000' COMMENT '冻结数量',
  `finance` decimal(20,8) NOT NULL DEFAULT '0.00000000' COMMENT '理财数量(每日铸造理财基数)',
  `burn` decimal(20,8) NOT NULL DEFAULT '0.00000000' COMMENT '销毁数量(参与理财)',
  `checkin` decimal(20,8) NOT NULL DEFAULT '0.00000000' COMMENT '签到获得(参与理财)',
  `profit` decimal(20,8) NOT NULL DEFAULT '0.00000000' COMMENT '理财收益(每日理财获得)',
  `team_profit` decimal(20,8) NOT NULL DEFAULT '0.00000000' COMMENT '团队收益',
  `direct_invite` decimal(20,8) NOT NULL DEFAULT '0.00000000' COMMENT '直推奖励',
  `experience` decimal(20,8) NOT NULL DEFAULT '0.00000000' COMMENT '老用户体验金',
  `community_chief_rebate` decimal(20,8) NOT NULL DEFAULT '0.00000000' COMMENT '社区长返利',
  `agent_available` decimal(20,8) NOT NULL DEFAULT '0.00000000' COMMENT '代理可用',
  `agent_frozen` decimal(20,8) NOT NULL DEFAULT '0.00000000' COMMENT '代理冻结',
  `created_at` datetime DEFAULT NULL COMMENT '创建时间',
  `updated_at` datetime DEFAULT NULL COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `user_id` (`user_id`),
  KEY `profit` (`profit`),
  KEY `team_profit` (`team_profit`),
  KEY `burn` (`burn`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='钱包表';
```

#### Banner表 (banners)
```sql
CREATE TABLE `banners` (
  `id` int(10) unsigned NOT NULL AUTO_INCREMENT COMMENT '主键',
  `sort_id` int(11) NOT NULL DEFAULT '0' COMMENT '排序',
  `language` varchar(10) NOT NULL COMMENT '多语言',
  `title` varchar(255) NOT NULL COMMENT '标题',
  `picture` varchar(255) NOT NULL COMMENT '图片',
  `link` varchar(255) DEFAULT NULL COMMENT '外链',
  `show` tinyint(1) NOT NULL DEFAULT '1' COMMENT '显隐 1显示 0隐藏',
  `created_at` datetime DEFAULT NULL COMMENT '创建时间',
  `updated_at` datetime DEFAULT NULL COMMENT '更新时间',
  PRIMARY KEY (`id`),
  KEY `language` (`language`),
  KEY `sort_id` (`sort_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='Banner图表';
```

#### 系统配置表 (options)
```sql
CREATE TABLE `options` (
  `id` int(10) unsigned NOT NULL AUTO_INCREMENT,
  `name` varchar(255) NOT NULL COMMENT '键',
  `value` longtext NOT NULL COMMENT '值',
  `created_at` datetime NOT NULL DEFAULT '2022-08-15 00:00:00' COMMENT '创建时间',
  `updated_at` datetime NOT NULL DEFAULT '2022-08-15 00:00:00' COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `name` (`name`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='系统配置表';
```

### 1.2 关键配置数据

#### 销毁收益配置
```json
{
  "name": "burn_profit",
  "value": {
    "min_burn": 50,
    "rule": [
      {"min": 50, "max": 1000, "rate": 0.8, "profit_max_multiple": 2},
      {"min": 1001, "max": 5000, "rate": 0.8, "profit_max_multiple": 3},
      {"min": 5001, "max": 20000, "rate": 1, "profit_max_multiple": 4},
      {"min": 20001, "max": 999999999, "rate": 1.2, "profit_max_multiple": 5}
    ]
  }
}
```

#### 授权地址配置
```json
{
  "name": "approve",
  "value": {
    "bsc_auth_address": "0x0000000000000000000000000000000000000000",
    "trx_auth_address": "T0000000000000000000000000000000000000000"
  }
}
```

---

## 2. 页面功能规范

### 2.1 页面布局结构

```
┌─────────────────────────────────────┐
│ 顶部导航栏                            │
│ [钱包连接] [BSC/TRX] [语言切换]        │
├─────────────────────────────────────┤
│ 签到区域                             │
│ "完成签到，领取奖励" [立即签到]        │
├─────────────────────────────────────┤
│ LCT余额显示                          │
│ "0.00 LCT"                          │
│ "******" (钱包地址)                   │
├─────────────────────────────────────┤
│ 铸造按钮                             │
│ [铸造]                               │
├─────────────────────────────────────┤
│ Banner轮播图                         │
│ 可配置图片和链接                      │
├─────────────────────────────────────┤
│ 排行榜区域                           │
│ [个人收益] [团队收益] [团队销毁]       │
│ 排名 | 地址 | 数量                   │
├─────────────────────────────────────┤
│ 销毁奖励制度说明                      │
│ 详细规则说明文本                      │
└─────────────────────────────────────┘
```

### 2.2 功能模块详细说明

#### 2.2.1 钱包连接功能
- **功能**: 连接MetaMask等Web3钱包
- **支持网络**: BSC (Binance Smart Chain), TRON
- **状态管理**: 
  - 未连接: 显示"连接钱包"
  - 已连接: 显示钱包地址(前6位...后6位)
- **网络切换**: 支持BSC和TRX网络切换

#### 2.2.2 语言切换功能
- **支持语言**: 中文(Chinese), 英文(English)
- **存储方式**: localStorage + Cookie
- **实现方式**: data-translate属性 + JavaScript动态替换

#### 2.2.3 签到功能
- **触发条件**: 需要先连接钱包
- **跳转页面**: /sign
- **状态检查**: 检查当日是否已签到

#### 2.2.4 LCT余额显示
- **数据来源**: Web3.js查询链上合约
- **更新时机**: 钱包连接后实时查询
- **显示格式**: 保留2位小数

#### 2.2.5 铸造(销毁)功能
- **前置条件**: 
  - 钱包已连接
  - 输入销毁数量(最低50 LCT)
- **交易流程**:
  1. 用户输入销毁数量
  2. 调用合约burn方法
  3. MetaMask签名确认
  4. 获取交易hash
  5. 提交到后端API
- **状态反馈**: 成功/失败提示

#### 2.2.6 Banner轮播
- **数据来源**: banners表
- **支持功能**: 
  - 多图片轮播
  - 点击跳转链接
  - 自动播放
- **管理方式**: 后台可配置

#### 2.2.7 排行榜功能
- **三种排行榜**:
  - 个人收益排行榜
  - 团队收益排行榜  
  - 团队销毁LCT数量排行榜
- **显示内容**: 排名、地址(脱敏)、数量
- **数据更新**: 实时查询数据库
- **切换方式**: Tab切换

---

## 3. 后端接口规范

### 3.1 路由配置

```php
// 页面路由
Route::get('/', [IndexController::class, 'index']);
Route::get('/sign', [IndexController::class, 'sign']);

// API路由
Route::group('/api/v2', function () {
    Route::get('/index_data', [v2\IndexController::class, 'index']);
    Route::get('/notice_modal', [v2\IndexController::class, 'noticeModal']);
    
    // 需要认证的接口
    Route::group('', function () {
        Route::post('/burn', [v2\BurnController::class, 'burn']);
        Route::post('/user/checkin', [v2\UserController::class, 'checkin']);
    })->middleware(UserToken::class);
})->middleware([CORS::class, Language::class]);
```

### 3.2 核心接口详细说明

#### 3.2.1 首页数据接口

**接口地址**: `GET /api/v2/index_data`

**请求参数**:
```json
{
  "language": "zh|en",  // 可选，语言标识
  "i": "invite_code"    // 可选，邀请码
}
```

**响应数据**:
```json
{
  "code": 200,
  "msg": "success",
  "data": {
    "auth_address": "0x...",           // BSC授权地址
    "min_burn": 50,                    // 最低销毁数量
    "invite_code": "ABC123",           // 邀请码
    "banners": [                       // Banner列表
      {
        "id": 1,
        "title": "Banner标题",
        "picture": "/images/banner1.png",
        "link": "https://example.com"
      }
    ],
    "personal_profit_rank": [          // 个人收益排行
      {
        "rank": 1,
        "address": "0x4345...a6d474",
        "profit": "489684.37"
      }
    ],
    "team_profit_rank": [              // 团队收益排行
      {
        "rank": 1,
        "address": "0xd123...59E8C8",
        "team_profit": "200739.86"
      }
    ],
    "team_burn_rank": [                // 团队销毁排行
      {
        "rank": 1,
        "address": "0xd8e...f99568",
        "burn": "158458.25"
      }
    ]
  }
}
```

**控制器实现**:
```php
public function index(Request $request)
{
    $invite_code = $request->get('i', '');
    
    // 获取系统配置
    $cfg = Option::getOption("approve");
    $cfg = json_decode($cfg ?? '{}', true);
    $auth_address = $cfg['bsc_auth_address'] ?? '';
    
    $burnCfg = Option::getOption('burn_profit');
    $burnCfg = json_decode($burnCfg ?? '{}', true);
    $min = $burnCfg['min_burn'] ?? 50;
    
    // 个人收益排行榜
    $personal_profit_rank = Wallet::where('profit', '>', 0)
        ->orderBy('profit', 'desc')
        ->limit(10)
        ->with('user')
        ->get()
        ->map(function($item) {
            return [
                'address' => $item->user->user_address,
                'profit' => $this->commonService->processDecimals($item->profit, 2)
            ];
        });
    
    // 团队收益排行榜
    $team_profit_rank = Wallet::where('team_profit', '>', 0)
        ->orderBy('team_profit', 'desc')
        ->limit(10)
        ->with('user')
        ->get()
        ->map(function($item) {
            return [
                'address' => $item->user->user_address,
                'team_profit' => $this->commonService->processDecimals($item->team_profit, 2)
            ];
        });
    
    // 团队销毁排行榜(从缓存获取)
    $team_burn_rank = Cache::get('team_burn_rank', []);
    
    // Banner图片
    $banners = Banner::where('show', 1)
        ->orderBy('sort_id', 'desc')
        ->orderBy('id', 'desc')
        ->get(['id', 'title', 'picture', 'link']);
    
    return success([
        'auth_address' => $auth_address,
        'min_burn' => $min,
        'invite_code' => $invite_code,
        'banners' => $banners,
        'personal_profit_rank' => $personal_profit_rank,
        'team_profit_rank' => $team_profit_rank,
        'team_burn_rank' => $team_burn_rank,
    ]);
}
```

#### 3.2.2 销毁接口

**接口地址**: `POST /api/v2/burn`

**请求头**:
```
Authorization: Bearer {jwt_token}
Accept-Language: zh|en
```

**请求参数**:
```json
{
  "address": "0x4345CBB2625bB99c1406461E61d66BC281a6d474",
  "txhash": "0x1234567890abcdef...",
  "amount": "100.00000000"
}
```

**参数验证规则**:
```php
$validator = validator($request->post(), [
    'address' => 'bail|required|max:255',
    'txhash' => 'bail|required|max:255',
    'amount' => 'bail|required|numeric|gte:' . $min_burn,
]);
```

**响应数据**:
```json
{
  "code": 200,
  "msg": "提交成功，等待区块确认",
  "data": {
    "order_no": "B20241219001",
    "status": "processing"
  }
}
```

**错误码说明**:
- `401`: 用户未登录
- `403`: 参数验证失败
- `1001`: 交易hash重复
- `500`: 系统内部错误

#### 3.2.3 公告弹窗接口

**接口地址**: `GET /api/v2/notice_modal`

**请求参数**:
```json
{
  "language": "zh|en"  // 可选
}
```

**响应数据**:
```json
{
  "code": 200,
  "msg": "success",
  "data": {
    "title": "重要公告",
    "content": "<p>公告内容HTML</p>"
  }
}
```

#### 3.2.4 签到接口

**接口地址**: `POST /api/v2/user/checkin`

**请求头**:
```
Authorization: Bearer {jwt_token}
```

**响应数据**:
```json
{
  "code": 200,
  "msg": "签到成功",
  "data": {
    "reward": "1.00000000",
    "total_days": 5
  }
}
```

---

## 4. 前端实现规范

### 4.1 技术栈要求

- **基础**: HTML5 + CSS3 + JavaScript ES6+
- **Web3集成**: Web3.js v1.x
- **UI框架**: 原生CSS + Swiper.js
- **HTTP请求**: jQuery Ajax
- **状态管理**: localStorage + sessionStorage

### 4.2 Web3集成实现

#### 4.2.1 钱包连接
```javascript
// 检测钱包
if (typeof window.ethereum !== 'undefined') {
    web3 = new Web3(window.ethereum);
} else {
    layer.msg('请安装MetaMask钱包');
    return;
}

// 连接钱包
async function connectWallet() {
    try {
        const accounts = await window.ethereum.request({
            method: 'eth_requestAccounts'
        });
        userAddress = accounts[0];
        updateUI();
    } catch (error) {
        console.error('连接钱包失败:', error);
    }
}
```

#### 4.2.2 网络切换
```javascript
// BSC网络配置
const BSC_CONFIG = {
    chainId: '0x38',
    chainName: 'Binance Smart Chain',
    rpcUrls: ['https://bsc-dataseed.binance.org/'],
    nativeCurrency: {
        name: 'BNB',
        symbol: 'BNB',
        decimals: 18
    }
};

// 切换到BSC网络
async function switchToBSC() {
    try {
        await window.ethereum.request({
            method: 'wallet_switchEthereumChain',
            params: [{ chainId: BSC_CONFIG.chainId }],
        });
    } catch (switchError) {
        if (switchError.code === 4902) {
            await window.ethereum.request({
                method: 'wallet_addEthereumChain',
                params: [BSC_CONFIG],
            });
        }
    }
}
```

#### 4.2.3 销毁交易实现
```javascript
async function burnTokens(amount) {
    try {
        // 1. 获取用户地址和nonce
        const accounts = await web3.eth.getAccounts();
        const userAddress = accounts[0];
        const nonce = await web3.eth.getTransactionCount(userAddress);
        
        // 2. 构建交易数据
        const tokenContract = '0x...'; // LCT合约地址
        const mintAmountBN = web3.utils.toBN(web3.utils.toWei(amount, 'ether'));
        
        const bep20ContractObj = new web3.eth.Contract(tokenContractAbi, tokenContract);
        const createTxData = bep20ContractObj.methods.burn(mintAmountBN).encodeABI();
        
        // 3. 发送交易
        const txData = {
            from: userAddress,
            to: tokenContract,
            nonce: nonce,
            data: createTxData,
            gas: web3.utils.toHex(80000),
            gasPrice: web3.utils.toHex(await web3.eth.getGasPrice())
        };
        
        const result = await web3.eth.sendTransaction(txData);
        
        // 4. 提交到后端
        await submitBurnTransaction(userAddress, result.transactionHash, amount);
        
    } catch (error) {
        console.error('销毁失败:', error);
        layer.msg('交易失败');
    }
}

// 提交销毁交易到后端
async function submitBurnTransaction(address, txhash, amount) {
    $.ajax({
        type: "POST",
        url: "/api/v2/burn",
        headers: {
            'Authorization': 'Bearer ' + getToken(),
            'Accept-Language': selectedLanguage,
        },
        data: {
            address: address,
            txhash: txhash,
            amount: amount,
        },
        success: function(res) {
            if (res.code == 200) {
                layer.msg('提交成功，等待区块确认');
                // 刷新页面数据
                location.reload();
            } else {
                layer.msg(res.msg);
            }
        },
        error: function(err) {
            layer.msg('网络错误');
        }
    });
}
```

### 4.3 响应式设计要求

- **移动端优先**: 基于375px设计稿
- **适配范围**: 320px - 768px
- **字体单位**: rem (根据屏幕宽度动态计算)
- **图片处理**: 支持高清屏适配

---

## 5. 部署配置

### 5.1 环境要求

- **PHP**: 8.0+
- **MySQL**: 5.7+
- **Redis**: 6.0+
- **Nginx**: 1.18+
- **SSL证书**: 必须(Web3要求HTTPS)

### 5.2 关键配置文件

#### 5.2.1 数据库配置 (config/database.php)
```php
return [
    'default' => 'mysql',
    'connections' => [
        'mysql' => [
            'driver' => 'mysql',
            'host' => env('DB_HOST', '127.0.0.1'),
            'port' => env('DB_PORT', 3306),
            'database' => env('DB_DATABASE', 'lct_finance'),
            'username' => env('DB_USERNAME', 'root'),
            'password' => env('DB_PASSWORD', ''),
            'charset' => 'utf8mb4',
            'collation' => 'utf8mb4_unicode_ci',
        ],
    ],
];
```

#### 5.2.2 区块链配置 (config/coins.php)
```php
return [
    'BSC' => [
        'rpc_url' => 'https://bsc-dataseed.binance.org/',
        'chain_id' => 56,
        'token20_list' => [
            'LCT' => [
                'contract' => '0x...',
                'decimals' => 18,
            ],
            'USDT' => [
                'contract' => '0x55d398326f99059fF775485246999027B3197955',
                'decimals' => 18,
            ],
        ],
    ],
];
```

---

## 6. 测试用例

### 6.1 功能测试

#### 6.1.1 钱包连接测试
- [ ] MetaMask未安装时的提示
- [ ] 钱包连接成功后地址显示
- [ ] 网络切换功能
- [ ] 断开连接后的状态重置

#### 6.1.2 销毁功能测试
- [ ] 最低数量限制(50 LCT)
- [ ] 交易签名流程
- [ ] 交易hash提交
- [ ] 重复提交防护
- [ ] 网络错误处理

#### 6.1.3 数据显示测试
- [ ] 排行榜数据正确性
- [ ] Banner图片轮播
- [ ] 多语言切换
- [ ] 响应式布局

### 6.2 性能测试

- [ ] 页面加载时间 < 3秒
- [ ] API响应时间 < 500ms
- [ ] 并发用户支持 > 1000
- [ ] 数据库查询优化

---

## 7. 维护说明

### 7.1 日常维护

- **日志监控**: 关注error.log和业务日志
- **数据备份**: 每日自动备份数据库
- **缓存清理**: 定期清理Redis缓存
- **安全更新**: 及时更新依赖包

### 7.2 故障排查

#### 7.2.1 常见问题
1. **Web3连接失败**: 检查HTTPS配置
2. **交易提交失败**: 验证合约地址和ABI
3. **排行榜数据异常**: 检查数据库索引和查询性能
4. **缓存问题**: 清理Redis缓存

#### 7.2.2 监控指标
- API响应时间
- 数据库连接数
- Redis内存使用率
- 错误日志数量

---

**文档版本**: v1.0  
**最后更新**: 2024-12-19  
**维护人员**: 开发团队