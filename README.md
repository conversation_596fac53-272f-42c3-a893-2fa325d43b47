# LCT金融系统微服务架构

## 项目概述

LCT金融系统是一个基于Spring Cloud微服务架构的区块链理财平台，支持BSC和TRON网络，提供用户管理、钱包管理、代币兑换、代理分佣等核心功能。

## 架构设计

### 技术栈
- **后端**: Spring Boot 3.2.1 + Spring Cloud 2023.0.0
- **注册中心**: Nacos 2.2.0
- **数据库**: MySQL 8.0
- **缓存**: Redis 7
- **消息队列**: RabbitMQ 3.12
- **容器化**: Docker + Docker Compose

### 微服务架构

```
┌─────────────────────────────────────────────────────────────────┐
│                        LCT金融系统微服务架构                      │
├─────────────────────────────────────────────────────────────────┤
│  API网关层 (Spring Cloud Gateway)                               │
│  └── lct-gateway (8080)                                        │
├─────────────────────────────────────────────────────────────────┤
│  业务服务层 (Spring Boot)                                       │
│  ├── lct-auth-service (8081)      # 认证服务                   │
│  ├── lct-user-service (8082)      # 用户服务                   │
│  ├── lct-wallet-service (8083)    # 钱包服务                   │
│  ├── lct-exchange-service (8084)  # 交易服务                   │
│  ├── lct-agent-service (8085)     # 代理服务                   │
│  ├── lct-admin-service (8086)     # 管理服务                   │
│  ├── lct-notification-service (8087) # 通知服务                │
│  └── lct-blockchain-service (8088) # 区块链服务                │
├─────────────────────────────────────────────────────────────────┤
│  基础设施层                                                     │
│  ├── Nacos (8848)                 # 注册中心和配置中心          │
│  ├── MySQL (3306)                 # 数据库集群                 │
│  ├── Redis (6379)                 # 缓存服务                   │
│  └── RabbitMQ (5672/15672)        # 消息队列                   │
└─────────────────────────────────────────────────────────────────┘
```

## 当前进展

### ✅ 已完成组件

#### 1. 项目基础架构
- [x] **父级Maven项目** - 统一依赖管理和版本控制
- [x] **lct-common共享模块** - 基础实体类、工具类、异常处理
- [x] **Docker基础设施** - 完整的容器化部署环境

#### 2. 网关和基础服务
- [x] **lct-gateway** - API网关服务，路由转发和负载均衡
- [x] **lct-auth-service** - 认证服务，JWT认证和权限管理
- [x] **lct-user-service** - 用户服务（进行中）

#### 3. 基础设施组件
- [x] **Nacos** - 服务注册发现和配置管理
- [x] **MySQL** - 数据库集群配置
- [x] **Redis** - 缓存服务配置
- [x] **RabbitMQ** - 消息队列配置

### 🚧 进行中

- **lct-user-service** - 用户管理服务开发

### 📋 待开发

- **lct-wallet-service** - 钱包管理服务
- **lct-exchange-service** - 交易服务
- **lct-agent-service** - 代理服务
- **lct-admin-service** - 管理服务
- **lct-notification-service** - 通知服务
- **lct-blockchain-service** - 区块链服务

## 项目结构

```
lct/
├── pom.xml                          # 父级Maven项目
├── docker-compose.yml               # Docker容器编排
├── README.md                        # 项目说明文档
├── TASK_MICROSERVICES_MIGRATION.md  # 迁移任务跟踪
│
├── lct-common/                      # 共享模块 ✅
│   ├── src/main/java/com/lct/common/
│   │   ├── entity/BaseEntity.java   # 基础实体类
│   │   ├── dto/Result.java          # 统一响应格式
│   │   ├── exception/               # 异常处理
│   │   ├── utils/JwtUtils.java      # JWT工具类
│   │   └── constant/ErrorCode.java  # 错误码定义
│   └── pom.xml
│
├── lct-gateway/                     # API网关 ✅
│   ├── src/main/java/com/lct/gateway/
│   │   └── GatewayApplication.java  # 网关启动类
│   ├── src/main/resources/
│   │   └── application.yml          # 网关配置
│   └── pom.xml
│
├── lct-auth-service/                # 认证服务 ✅
│   ├── src/main/java/com/lct/auth/
│   │   ├── controller/AuthController.java
│   │   ├── entity/Admin.java
│   │   ├── dto/LoginRequest.java
│   │   └── AuthServiceApplication.java
│   ├── src/main/resources/
│   │   └── application.yml
│   └── pom.xml
│
├── lct-user-service/                # 用户服务 🚧
│   ├── src/main/java/com/lct/user/
│   └── pom.xml
│
└── docker/                          # Docker配置 ✅
    ├── mysql/init/init.sql          # 数据库初始化
    └── redis/redis.conf             # Redis配置
```

## 快速开始

### 环境要求
- Java 17+
- Maven 3.8+
- Docker & Docker Compose

### 启动基础设施
```bash
# 启动基础设施服务
docker-compose up -d nacos mysql redis rabbitmq

# 查看服务状态
docker-compose ps
```

### 访问地址
- **Nacos控制台**: http://139.9.189.80:8848/nacos (nacos/nacos)
- **RabbitMQ管理界面**: http://139.9.189.80:15672 (admin/admin123456)
- **API网关**: http://139.9.189.80:8080

### 编译和运行
```bash
# 编译整个项目
mvn clean compile

# 启动网关服务
cd lct-gateway && mvn spring-boot:run

# 启动认证服务
cd lct-auth-service && mvn spring-boot:run
```

## 开发规范

### API设计
- 统一使用RESTful风格
- 响应格式使用`Result<T>`封装
- 错误码使用`ErrorCode`常量

### 数据库设计
- 每个微服务独立数据库
- 统一使用`BaseEntity`基础实体
- 支持逻辑删除和审计字段

### 服务通信
- 通过Nacos进行服务发现
- 使用OpenFeign进行服务间调用
- 重要操作使用消息队列异步处理

## 部署说明

### Docker部署
```bash
# 构建所有服务镜像
docker-compose build

# 启动完整系统
docker-compose up -d

# 查看日志
docker-compose logs -f [service-name]
```

### 监控和运维
- 服务健康检查通过Spring Boot Actuator
- 日志统一输出到控制台，便于容器化部署
- 支持通过Nacos动态配置管理

---

**当前版本**: v1.0.0  
**最后更新**: 2024年12月  
**维护团队**: LCT架构组 