package com.lct.auth.controller;

import com.lct.auth.dto.LoginRequest;
import com.lct.auth.service.AuthService;
import com.lct.common.dto.Result;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;

/**
 * 认证控制器
 * 
 * <AUTHOR> Team
 * @version 1.0
 */
@RestController
@RequestMapping("/auth")
@RequiredArgsConstructor
public class AuthController {
    
    private final AuthService authService;
    
    /**
     * 管理员登录
     */
    @PostMapping("/admin/login")
    public Result<String> adminLogin(@Valid @RequestBody LoginRequest request) {
        String token = authService.adminLogin(request.getUsername(), request.getPassword());
        return Result.success("登录成功", token);
    }
    
    /**
     * 钱包签名登录
     */
    @PostMapping("/wallet/login")
    public Result<String> walletLogin(@RequestParam String address, 
                                     @RequestParam String signature, 
                                     @RequestParam String message,
                                     @RequestParam(defaultValue = "BSC") String chain) {
        String token = authService.walletLogin(address, signature, message, chain);
        return Result.success("登录成功", token);
    }
    
    /**
     * 验证Token
     */
    @GetMapping("/verify")
    public Result<Object> verifyToken(@RequestHeader("Authorization") String token) {
        Object userInfo = authService.verifyToken(token);
        return Result.success("验证成功", userInfo);
    }
    
    /**
     * 退出登录
     */
    @PostMapping("/logout")
    public Result<Void> logout(@RequestHeader("Authorization") String token) {
        authService.logout(token);
        return Result.success("退出成功");
    }
} 