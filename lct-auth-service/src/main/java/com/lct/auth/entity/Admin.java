package com.lct.auth.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.lct.common.entity.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 管理员实体类
 * 
 * <AUTHOR> Team
 * @version 1.0
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("wa_admins")
public class Admin extends BaseEntity {
    
    /**
     * 用户名
     */
    private String username;
    
    /**
     * 密码
     */
    private String password;
    
    /**
     * 昵称
     */
    private String nickname;
    
    /**
     * 头像
     */
    private String avatar;
    
    /**
     * 邮箱
     */
    private String email;
    
    /**
     * 手机号
     */
    private String mobile;
    
    /**
     * 状态：1-正常，0-禁用
     */
    private Integer status;
    
    /**
     * 最后登录时间
     */
    private java.time.LocalDateTime lastLoginAt;
    
    /**
     * 最后登录IP
     */
    private String lastLoginIp;
} 