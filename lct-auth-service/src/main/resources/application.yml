server:
  port: 8081

spring:
  application:
    name: lct-auth-service
  profiles:
    active: dev
  cloud:
    nacos:
      discovery:
        server-addr: ************:8848
        namespace: lct-finance
      config:
        server-addr: ************:8848
        namespace: lct-finance
        file-extension: yml
  
  # 数据库配置
  datasource:
    url: jdbc:mysql://************:3306/lct_auth?useUnicode=true&characterEncoding=utf8&useSSL=false&serverTimezone=Asia/Shanghai
    username: lct_user
    password: lct123456
    driver-class-name: com.mysql.cj.jdbc.Driver
  
  # Redis配置
  redis:
    host: ************
    port: 6379
    database: 1
    timeout: 3000ms
    lettuce:
      pool:
        max-active: 8
        max-idle: 8
        min-idle: 0

# MyBatis Plus配置
mybatis-plus:
  configuration:
    map-underscore-to-camel-case: true
    log-impl: org.apache.ibatis.logging.stdout.StdOutImpl
  global-config:
    db-config:
      logic-delete-field: deleted
      logic-delete-value: 1
      logic-not-delete-value: 0

# JWT配置
jwt:
  secret: lct-finance-auth-service-secret-key
  expiration: 86400

# 日志配置
logging:
  level:
    com.lct.auth: DEBUG
    com.baomidou.mybatisplus: DEBUG 